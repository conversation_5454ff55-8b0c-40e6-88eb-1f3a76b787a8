# YOLO相机参数优化测试程序 (test3.py)

## 功能说明

这个程序会自动测试不同的Astra相机参数组合，找出在YOLO目标检测任务中表现最佳的增益和伽马参数设置。

## 主要特性

- 🔧 **自动参数测试**: 自动遍历不同的增益和伽马参数组合
- 🎯 **YOLO集成**: 使用您的train_8.pt模型进行实时目标检测
- 📊 **智能评分**: 综合考虑检测数量、置信度和类别多样性
- 💾 **结果保存**: 自动保存详细的测试结果到JSON文件
- 📈 **实时反馈**: 显示每个参数组合的测试进度和结果

## 测试参数

### 增益范围 (GAIN_RANGE)
- 测试值: [10, 25, 50, 75, 100, 128]
- 影响: 图像亮度和噪声水平

### 伽马范围 (GAMMA_RANGE)  
- 测试值: [100, 125, 150, 174, 200, 225]
- 影响: 图像对比度和细节表现

### 固定参数
- 饱和度: 128
- 对比度: 64  
- 亮度: 0

## 评分机制

程序使用综合评分系统评估每个参数组合:

```
综合得分 = 平均检测数量 × 0.5 + 平均置信度 × 0.3 + 类别多样性 × 0.2
```

- **平均检测数量**: 每帧平均检测到的目标数量
- **平均置信度**: 所有检测结果的平均置信度
- **类别多样性**: 检测到的不同类别数量

## 使用方法

### 1. 环境准备

确保已安装必要的依赖:
```bash
pip install ultralytics opencv-python numpy pyorbbecsdk
```

### 2. 模型文件

确保 `train_8.pt` 文件在当前目录下。

### 3. 运行测试

```bash
python test3.py
```

### 4. 测试过程

程序将自动执行以下步骤:
1. 加载YOLO模型
2. 初始化Astra相机
3. 遍历所有参数组合 (6×6=36组)
4. 每组参数测试10帧图像
5. 计算综合得分
6. 显示最佳参数组合

## 输出结果

### 控制台输出
- 实时显示测试进度
- 每个参数组合的检测结果
- 最终排名和最佳参数

### JSON结果文件
程序会生成类似 `yolo_parameter_optimization_20241213_143022.json` 的文件，包含:
- 测试配置信息
- 最佳参数组合
- 所有测试结果的详细数据

## 结果解读

### 示例输出
```
🏆 最佳参数组合:
   增益: 75
   伽马: 174
   平均检测数: 3.2
   平均置信度: 0.756
   检测类别: bottle, orange, book, scissors
   综合得分: 2.427
```

### 参数建议
- **高检测数量**: 选择检测数量最多的参数
- **高置信度**: 选择置信度最高的参数  
- **平衡选择**: 选择综合得分最高的参数（推荐）

## 自定义配置

您可以修改以下参数来适应您的需求:

```python
# 测试参数范围
GAIN_RANGE = [10, 25, 50, 75, 100, 128]
GAMMA_RANGE = [100, 125, 150, 174, 200, 225]

# 测试配置
FRAMES_PER_TEST = 10  # 每组参数的测试帧数
CONFIDENCE_THRESHOLD = 0.25  # YOLO置信度阈值
```

## 注意事项

1. **测试时间**: 完整测试需要约15-20分钟
2. **环境光线**: 保持测试期间光线稳定
3. **目标物体**: 确保测试场景中有足够的目标物体
4. **相机连接**: 确保Astra相机正确连接

## 故障排除

### 常见问题
- **模型加载失败**: 检查train_8.pt文件是否存在
- **相机连接失败**: 检查Astra相机连接和驱动
- **检测结果为0**: 调整场景中的目标物体或光线条件

### 中断测试
按 `Ctrl+C` 可以安全中断测试，程序会保存已完成的结果。
