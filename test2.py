#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例程序：使用旧版本 pyorbbecsdk (main branch, v1.10.16) 调整 Astra Pro Plus 相机参数
功能：调整白平衡、增益、亮度、锐度、伽马、饱和度、对比度、色调
注意：
- 确保 pyorbbecsdk 已安装 (pip install pyorbbecsdk==1.10.16)
- Astra Pro Plus 可能不支持所有参数，程序会检查支持情况
- 按 'q' 退出
"""

import cv2
import numpy as np
from pyorbbecsdk import Pipeline, Config, OBSensorType, OBFormat, OBPropertyID
import time

def check_property_support(device, property_id, property_name):
    """
    检查设备是否支持指定属性
    """
    try:
        device.get_int_property(property_id)
        print(f"✅ {property_name} 属性支持")
        return True
    except Exception as e:
        print(f"⚠️ {property_name} 属性不支持: {e}")
        return False

def set_property_safely(device, property_id, value, property_name, prop_type="int"):
    """
    安全设置属性值，捕获可能的错误
    """
    try:
        if prop_type == "bool":
            device.set_bool_property(property_id, value)
        else:
            device.set_int_property(property_id, value)
        print(f"✅ 设置 {property_name} 为 {value}")
    except Exception as e:
        print(f"❌ 设置 {property_name} 失败: {e}")

def main():
    # 创建 Pipeline
    try:
        pipeline = Pipeline()
        print("✅ Pipeline 创建成功")
    except Exception as e:
        print(f"❌ Pipeline 创建失败: {e}")
        return

    # 获取 Device
    device = pipeline.get_device()
    if device is None:
        print("❌ 获取设备失败")
        pipeline.stop()
        return

    print(f"✅ 设备信息: {device.get_device_info().get_name()}, SN: {device.get_device_info().get_serial_number()}")

    # 检查和设置相机参数
    property_map = [
        (OBPropertyID.OB_PROP_COLOR_AUTO_WHITE_BALANCE_BOOL, True, "自动白平衡", "bool"),
        (OBPropertyID.OB_PROP_COLOR_WHITE_BALANCE_INT, 5000, "白平衡 (K)", "int"),
        (OBPropertyID.OB_PROP_COLOR_GAIN_INT, 80, "增益", "int"),
        (OBPropertyID.OB_PROP_COLOR_BRIGHTNESS_INT, 0, "亮度", "int"),
        (OBPropertyID.OB_PROP_COLOR_SHARPNESS_INT, 3, "锐度", "int"),
        (OBPropertyID.OB_PROP_COLOR_GAMMA_INT, 0, "伽马", "int"),
        (OBPropertyID.OB_PROP_COLOR_SATURATION_INT, 64, "饱和度", "int"),
        (OBPropertyID.OB_PROP_COLOR_CONTRAST_INT, 32, "对比度", "int"),
        (OBPropertyID.OB_PROP_COLOR_HUE_INT, 0, "色调", "int")
    ]

    for prop_id, value, name, prop_type in property_map:
        if check_property_support(device, prop_id, name):
            set_property_safely(device, prop_id, value, name, prop_type)

    # 配置颜色流
    config = Config()
    try:
        # 使用 OBSensorType.COLOR_SENSOR 获取颜色流配置文件
        profile_list = pipeline.get_stream_profile_list(OBSensorType.COLOR_SENSOR)
        color_profile = None
        # 优先选择 640x480 RGB 30fps
        try:
            color_profile = profile_list.get_video_stream_profile(640, 480, OBFormat.RGB, 30)
            print(f"✅ 找到配置文件: 640x480, RGB, 30fps")
        except Exception as e:
            print(f"⚠️ 未找到 640x480 RGB 30fps 配置文件: {e}")
            # 尝试其他分辨率（如 1280x720）
            try:
                color_profile = profile_list.get_video_stream_profile(1280, 720, OBFormat.RGB, 30)
                print(f"✅ 使用备用配置文件: 1280x720, RGB, 30fps")
            except Exception as e:
                print(f"⚠️ 未找到 1280x720 RGB 30fps 配置文件: {e}")
                # 遍历所有可用配置文件
                for i in range(profile_list.get_count()):
                    profile = profile_list.get_profile(i)
                    video_profile = profile.as_video_stream_profile()
                    if video_profile.get_format() == OBFormat.RGB:
                        color_profile = video_profile
                        print(f"✅ 使用备用配置文件: {video_profile.get_width()}x{video_profile.get_height()}, {video_profile.get_fps()}fps")
                        break
        if color_profile is None:
            print("❌ 无可用颜色流配置文件")
            pipeline.stop()
            return
        config.enable_stream(color_profile)
        print("✅ 颜色流配置完成")
    except Exception as e:
        print(f"❌ 颜色流配置失败: {e}")
        pipeline.stop()
        return

    # 启动 Pipeline
    try:
        pipeline.start(config)
        print("✅ Pipeline 启动成功")
    except Exception as e:
        print(f"❌ Pipeline 启动失败: {e}")
        pipeline.stop()
        return

    print("✅ 显示调整后的颜色流，按 'q' 退出")

    # 显示颜色流
    while True:
        try:
            frames = pipeline.wait_for_frames(100)
            if frames is None:
                continue
            color_frame = frames.get_color_frame()
            if color_frame is None:
                continue
            # 获取数据并转换为 OpenCV 图像
            data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
            color_image = np.reshape(data, (color_frame.get_height(), color_frame.get_width(), 3))
            color_image = cv2.cvtColor(color_image, cv2.COLOR_RGB2BGR)  # 转换为 BGR
            cv2.imshow("Adjusted Color Viewer", color_image)
            key = cv2.waitKey(1)
            if key == ord('q'):
                break
        except Exception as e:
            print(f"❌ 获取帧失败: {e}")
            time.sleep(0.1)  # 避免过快循环

    # 清理资源
    try:
        pipeline.stop()
        print("✅ Pipeline 已停止")
    except Exception as e:
        print(f"❌ Pipeline 停止失败: {e}")
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()