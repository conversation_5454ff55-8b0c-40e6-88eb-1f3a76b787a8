import time
import numpy as np
import cv2
import pyorbbecsdk as ob

# 固定参数设置（优化用于YOLO识别）
FIXED_GAIN = 25        # 固定增益值
FIXED_GAMMA = 174     # 固定伽马值
FIXED_SATURATION = 128  # 固定饱和度值
FIXED_CONTRAST = 64    # 固定对比度值

def decode_mjpg_frame(frame_data, width, height):
    """解码MJPG格式的帧数据"""
    try:
        # 将字节数据转换为numpy数组
        jpeg_data = np.frombuffer(frame_data, dtype=np.uint8)
        # 使用OpenCV解码JPEG数据
        image = cv2.imdecode(jpeg_data, cv2.IMREAD_COLOR)
        if image is None:
            print("JPEG解码失败")
            return None
        return image
    except Exception as e:
        print(f"解码MJPG帧失败: {e}")
        return None

def calculate_brightness(image):
    """计算图像平均亮度（灰度均值）"""
    if image is None:
        return 0
    
    # 检查图像格式和通道数
    if len(image.shape) == 3:
        if image.shape[2] == 3:  # BGR 或 RGB
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        elif image.shape[2] == 4:  # BGRA 或 RGBA
            gray = cv2.cvtColor(image, cv2.COLOR_BGRA2GRAY)
        else:
            # 如果是其他3通道格式，直接取第一个通道
            gray = image[:, :, 0]
    elif len(image.shape) == 2:
        # 已经是灰度图
        gray = image
    else:
        print(f"未知的图像格式: shape={image.shape}")
        return 0
    
    return np.mean(gray)

def set_fixed_parameters(device):
    """设置固定的图像参数"""
    try:
        print(f"🔧 设置固定参数:")
        print(f"   增益: {FIXED_GAIN}")
        print(f"   伽马: {FIXED_GAMMA}")
        print(f"   饱和度: {FIXED_SATURATION}")
        print(f"   对比度: {FIXED_CONTRAST}")
        
        # 设置增益
        device.set_int_property(ob.OBPropertyID.OB_PROP_COLOR_GAIN_INT, FIXED_GAIN)
        time.sleep(0.1)
        actual_gain = device.get_int_property(ob.OBPropertyID.OB_PROP_COLOR_GAIN_INT)
        print(f"✅ 增益设置: {FIXED_GAIN} -> 实际 {actual_gain}")
        
        # 设置伽马
        device.set_int_property(ob.OBPropertyID.OB_PROP_COLOR_GAMMA_INT, FIXED_GAMMA)
        time.sleep(0.1)
        actual_gamma = device.get_int_property(ob.OBPropertyID.OB_PROP_COLOR_GAMMA_INT)
        print(f"✅ 伽马设置: {FIXED_GAMMA} -> 实际 {actual_gamma}")
        
        # 设置饱和度
        device.set_int_property(ob.OBPropertyID.OB_PROP_COLOR_SATURATION_INT, FIXED_SATURATION)
        time.sleep(0.1)
        actual_saturation = device.get_int_property(ob.OBPropertyID.OB_PROP_COLOR_SATURATION_INT)
        print(f"✅ 饱和度设置: {FIXED_SATURATION} -> 实际 {actual_saturation}")
        
        # 设置对比度
        device.set_int_property(ob.OBPropertyID.OB_PROP_COLOR_CONTRAST_INT, FIXED_CONTRAST)
        time.sleep(0.1)
        actual_contrast = device.get_int_property(ob.OBPropertyID.OB_PROP_COLOR_CONTRAST_INT)
        print(f"✅ 对比度设置: {FIXED_CONTRAST} -> 实际 {actual_contrast}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置固定参数失败: {e}")
        return False

def main():
    # 初始化 SDK 上下文
    ctx = ob.Context()
    device_list = ctx.query_devices()
    if device_list.get_count() == 0:
        print("未检测到任何 Orbbec 设备！")
        return

    # 获取第一个设备
    device = device_list.get_device_by_index(0)
    print("使用设备：Astra Pro Plus")

    # 创建 pipeline
    pipeline = ob.Pipeline(device)

    # 启用彩色流配置
    config = ob.Config()
    profile_list = pipeline.get_stream_profile_list(ob.OBSensorType.COLOR_SENSOR)
    color_profile = profile_list.get_default_video_stream_profile()
    config.enable_stream(color_profile)
    
    try:
        pipeline.start(config)
        print("Pipeline 启动成功")
    except Exception as e:
        print(f"Pipeline 启动失败: {e}")
        return

    # 关闭自动曝光
    try:
        device.set_bool_property(ob.OBPropertyID.OB_PROP_COLOR_AUTO_EXPOSURE_BOOL, False)
        print("已关闭自动曝光")
        
        # 等待一下确保设置生效
        time.sleep(0.5)
        
        # 验证自动曝光是否关闭
        auto_exposure_status = device.get_bool_property(ob.OBPropertyID.OB_PROP_COLOR_AUTO_EXPOSURE_BOOL)
        print(f"自动曝光状态: {auto_exposure_status}")
        
        if auto_exposure_status:
            print("⚠️  警告: 自动曝光可能没有完全关闭")
        
    except Exception as e:
        print(f"设置自动曝光失败: {e}")

    # 设置固定参数
    print("\n=== 设置固定图像参数 ===")
    if not set_fixed_parameters(device):
        print("⚠️  部分参数设置失败，但程序将继续运行")

    frame_count = 0
    
    print(f"\n=== 固定参数图像采集启动 ===")
    print(f"固定增益: {FIXED_GAIN}")
    print(f"固定伽马: {FIXED_GAMMA}")
    print(f"固定饱和度: {FIXED_SATURATION}")
    print(f"固定对比度: {FIXED_CONTRAST}")
    print("🎯 优化用于YOLO目标识别")
    print("=" * 30)
    
    try:
        while True:
            # 增加等待时间，避免超时
            frameset = pipeline.wait_for_frames(5000)  # 增加到5秒
            
            # 检查frameset是否为空
            if frameset is None:
                print("等待帧超时，重试...")
                continue
                
            color_frame = frameset.get_color_frame()
            if color_frame is None:
                print("未获取到彩色帧，跳过...")
                continue

            # 获取图像数据并解码
            frame_data = color_frame.get_data()
            width = color_frame.get_width()
            height = color_frame.get_height()
            
            # 添加调试信息
            if frame_count == 0:
                print(f"帧格式: {color_frame.get_format()}")
                print(f"帧大小: {width}x{height}")
                print(f"数据大小: {len(frame_data)} 字节")
            
            # 解码MJPG数据
            image = decode_mjpg_frame(frame_data, width, height)
            if image is None:
                print("图像解码失败，跳过...")
                continue
            
            # 计算图像亮度
            brightness = calculate_brightness(image)
            
            frame_count += 1

            # 显示图像信息
            cv2.putText(image, f"Gain: {FIXED_GAIN}", (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            cv2.putText(image, f"Gamma: {FIXED_GAMMA}", (10, 60),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(image, f"Saturation: {FIXED_SATURATION}", (10, 90),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
            cv2.putText(image, f"Contrast: {FIXED_CONTRAST}", (10, 120),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(image, f"Brightness: {brightness:.1f}", (10, 150),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            cv2.putText(image, f"Frame: {frame_count}", (10, 180),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(image, f"YOLO Optimized", (10, 210),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.imshow("Color Stream (Fixed Parameters for YOLO)", image)

            # 按 ESC 退出
            if cv2.waitKey(1) & 0xFF == 27:
                break

    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"运行时错误: {e}")
    finally:
        pipeline.stop()
        cv2.destroyAllWindows()
        print("程序已退出")

if __name__ == "__main__":
    main()
