# YOLO相机参数精细调优程序 (test4.py)

## 概述

基于test3.py的测试结果，test4.py对最佳参数组合周围进行精细调优。

**test3.py最佳结果**: 增益=100, 伽马=200, 得分=4.410

## 改进特性

### 🎯 **精细调优范围**
- **增益范围**: [85, 90, 95, 100, 105, 110, 115] (围绕100)
- **伽马范围**: [180, 185, 190, 195, 200, 205, 210, 215] (围绕200)
- **总组合数**: 7×8 = 56组参数

### 📊 **提高测试精度**
- **测试帧数**: 15帧/组合 (比test3.py增加50%)
- **重复测试**: 每组参数测试2轮
- **稳定性评估**: 计算标准差评估参数稳定性

### 🔍 **详细分析**
- 多轮测试平均结果
- 稳定性指标 (标准差)
- 与test3.py结果对比
- 详细的帧级检测数据

## 测试配置

```python
# 精细调优范围
GAIN_RANGE = [85, 90, 95, 100, 105, 110, 115]
GAMMA_RANGE = [180, 185, 190, 195, 200, 205, 210, 215]

# 测试配置
FRAMES_PER_TEST = 15      # 每组参数测试帧数
REPEAT_TESTS = 2          # 重复测试轮数
STABILIZATION_TIME = 2    # 参数稳定时间
```

## 评分机制

### 综合得分公式
```
综合得分 = 平均检测数量 × 0.5 + 平均置信度 × 0.3 + 类别多样性 × 0.2
```

### 稳定性评估
```
稳定性得分 = 1.0 / (1.0 + 标准差)
```
- 标准差越小，稳定性得分越高
- 稳定性得分范围: 0-1，越接近1越稳定

## 使用方法

### 1. 运行测试
```bash
python test4.py
```

### 2. 测试过程
1. 加载YOLO模型 (train_8.pt)
2. 初始化Astra相机
3. 遍历56组参数组合
4. 每组参数进行2轮×15帧测试
5. 计算平均结果和稳定性
6. 输出最佳参数组合

### 3. 预计时间
- **总测试时间**: 约25-30分钟
- **每组参数**: 约30秒 (2轮×15帧)

## 输出结果

### 控制台输出示例
```
🎯 开始YOLO参数精细调优
📋 基于test3.py最佳结果: 增益=100, 伽马=200 (得分=4.410)
🔍 精细调优范围: 增益 [85, 90, 95, 100, 105, 110, 115], 伽马 [180, 185, 190, 195, 200, 205, 210, 215]

📍 进度: 28/56
🧪 精细测试参数组合: 增益=100, 伽马=200
📸 第 1/2 轮测试，采集 15 帧...
  帧 1: 检测到 6 个目标, 置信度: 0.542
  ...
📊 最终结果: 平均检测数=6.23±0.15, 平均置信度=0.548±0.023, 类别数=5, 平均得分=4.387±0.045

🏆 最佳参数组合:
   增益: 105
   伽马: 195
   平均检测数: 6.47 ± 0.12
   平均置信度: 0.562 ± 0.018
   检测类别: toothbrush, box, remote, hanger, jelly
   平均得分: 4.523 ± 0.032

📊 与test3.py最佳结果比较:
   test3.py最佳: 增益=100, 伽马=200, 得分=4.410
   test4.py最佳: 增益=105, 伽马=195, 得分=4.523
   🎉 改进: +2.6%
```

### JSON结果文件
生成类似 `yolo_fine_tuning_20241213_164523.json` 的文件，包含:

```json
{
  "test_config": {
    "gain_range": [85, 90, 95, 100, 105, 110, 115],
    "gamma_range": [180, 185, 190, 195, 200, 205, 210, 215],
    "frames_per_test": 15,
    "repeat_tests": 2,
    "base_result": "test3.py最佳: 增益=100, 伽马=200, 得分=4.410"
  },
  "best_params": {
    "gain": 105,
    "gamma": 195,
    "avg_score": 4.523,
    "score_stability": 0.032,
    ...
  },
  "all_results": [...]
}
```

## 结果解读

### 排名表格
```
排名   增益     伽马     平均检测数      平均置信度      类别数      平均得分      稳定性
1    105    195    6.47         0.562        5        4.523      0.969
2    100    200    6.23         0.548        5        4.387      0.957
3    110    190    6.12         0.551        5        4.341      0.943
...
```

### 关键指标
- **平均检测数**: 每帧平均检测到的目标数量
- **平均置信度**: 所有检测的平均置信度
- **类别数**: 检测到的不同类别总数
- **平均得分**: 综合评分的平均值
- **稳定性**: 多轮测试的稳定性评分

## 参数选择建议

### 1. 最高得分优先
选择平均得分最高的参数组合，适合追求最佳检测效果。

### 2. 稳定性优先
选择稳定性得分高的参数组合，适合需要稳定表现的应用。

### 3. 平衡选择
综合考虑得分和稳定性，选择两者都较好的参数组合。

## 自定义配置

### 调整测试范围
```python
# 扩大搜索范围
GAIN_RANGE = [80, 85, 90, 95, 100, 105, 110, 115, 120]
GAMMA_RANGE = [170, 175, 180, 185, 190, 195, 200, 205, 210, 215, 220]

# 提高测试精度
FRAMES_PER_TEST = 20
REPEAT_TESTS = 3
```

### 调整评分权重
```python
# 更重视检测数量
score = (avg_detections_per_frame * 0.6 + 
         avg_confidence_overall * 0.25 + 
         class_diversity * 0.15)

# 更重视置信度
score = (avg_detections_per_frame * 0.4 + 
         avg_confidence_overall * 0.4 + 
         class_diversity * 0.2)
```

## 注意事项

1. **测试环境**: 保持与test3.py相同的测试环境
2. **目标物体**: 确保场景中有足够的目标物体
3. **光线条件**: 保持稳定的光线条件
4. **相机位置**: 避免测试期间移动相机

## 故障排除

### 参数设置失败
如果某些参数组合设置失败，程序会跳过并继续测试其他组合。

### 中断恢复
按 `Ctrl+C` 可安全中断，程序会保存已完成的测试结果。

### 性能优化
如果测试时间过长，可以减少 `FRAMES_PER_TEST` 或 `REPEAT_TESTS`。
