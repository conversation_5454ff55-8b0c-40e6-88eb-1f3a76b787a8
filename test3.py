#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动测试程序：优化Astra相机参数以获得最佳YOLO识别效果
功能：
- 自动测试不同的增益和伽马参数组合
- 使用YOLO模型进行目标检测
- 统计每个参数组合的检测数量和置信度
- 找出最佳参数组合
"""

import cv2
import numpy as np
import time
import json
import os
from datetime import datetime
from pyorbbecsdk import Pipeline, Config, OBSensorType, OBFormat, OBPropertyID
from ultralytics import YOLO

# 测试参数范围
GAIN_RANGE = [10, 25, 50, 75, 100, 128]  # 增益测试范围
GAMMA_RANGE = [100, 125, 150, 174, 200, 225]  # 伽马测试范围

# 固定参数
FIXED_SATURATION = 128
FIXED_CONTRAST = 64
FIXED_BRIGHTNESS = 0

# YOLO模型配置
MODEL_PATH = "train_8.pt"  # YOLO模型路径
CONFIDENCE_THRESHOLD = 0.25  # 置信度阈值
IOU_THRESHOLD = 0.45  # NMS IoU阈值

# 测试配置
FRAMES_PER_TEST = 10  # 每个参数组合测试的帧数
STABILIZATION_TIME = 2  # 参数设置后的稳定时间（秒）

class YOLOParameterOptimizer:
    def __init__(self):
        self.model = None
        self.pipeline = None
        self.device = None
        self.config = None
        self.results = []
        self.best_params = None
        self.best_score = 0
        
    def initialize_yolo_model(self):
        """初始化YOLO模型"""
        try:
            if not os.path.exists(MODEL_PATH):
                print(f"❌ YOLO模型文件不存在: {MODEL_PATH}")
                return False
            
            self.model = YOLO(MODEL_PATH)
            print(f"✅ YOLO模型加载成功: {MODEL_PATH}")
            return True
        except Exception as e:
            print(f"❌ YOLO模型加载失败: {e}")
            return False
    
    def initialize_camera(self):
        """初始化相机"""
        try:
            # 创建Pipeline
            self.pipeline = Pipeline()
            print("✅ Pipeline 创建成功")
            
            # 获取设备
            self.device = self.pipeline.get_device()
            if self.device is None:
                print("❌ 获取设备失败")
                return False
            
            print(f"✅ 设备信息: {self.device.get_device_info().get_name()}")
            
            # 配置颜色流
            self.config = Config()
            profile_list = self.pipeline.get_stream_profile_list(OBSensorType.COLOR_SENSOR)
            
            # 尝试不同的配置文件
            color_profile = None
            try:
                color_profile = profile_list.get_video_stream_profile(640, 480, OBFormat.RGB, 30)
                print("✅ 使用配置: 640x480, RGB, 30fps")
            except:
                try:
                    color_profile = profile_list.get_video_stream_profile(1280, 720, OBFormat.RGB, 30)
                    print("✅ 使用配置: 1280x720, RGB, 30fps")
                except:
                    # 使用默认配置
                    color_profile = profile_list.get_default_video_stream_profile()
                    print("✅ 使用默认配置")
            
            if color_profile is None:
                print("❌ 无可用颜色流配置")
                return False
            
            self.config.enable_stream(color_profile)
            
            # 启动Pipeline
            self.pipeline.start(self.config)
            print("✅ Pipeline 启动成功")
            
            # 关闭自动曝光
            try:
                self.device.set_bool_property(OBPropertyID.OB_PROP_COLOR_AUTO_EXPOSURE_BOOL, False)
                print("✅ 自动曝光已关闭")
            except Exception as e:
                print(f"⚠️ 关闭自动曝光失败: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 相机初始化失败: {e}")
            return False
    
    def set_camera_parameters(self, gain, gamma):
        """设置相机参数"""
        try:
            print(f"🔧 设置参数: 增益={gain}, 伽马={gamma}")
            
            # 设置增益
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_GAIN_INT, gain)
            time.sleep(0.1)
            
            # 设置伽马
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_GAMMA_INT, gamma)
            time.sleep(0.1)
            
            # 设置固定参数
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_SATURATION_INT, FIXED_SATURATION)
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_CONTRAST_INT, FIXED_CONTRAST)
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_BRIGHTNESS_INT, FIXED_BRIGHTNESS)
            
            # 等待参数稳定
            time.sleep(STABILIZATION_TIME)
            
            # 验证参数设置
            actual_gain = self.device.get_int_property(OBPropertyID.OB_PROP_COLOR_GAIN_INT)
            actual_gamma = self.device.get_int_property(OBPropertyID.OB_PROP_COLOR_GAMMA_INT)
            
            print(f"✅ 实际参数: 增益={actual_gain}, 伽马={actual_gamma}")
            return True
            
        except Exception as e:
            print(f"❌ 参数设置失败: {e}")
            return False
    
    def capture_frame(self):
        """捕获一帧图像"""
        try:
            frames = self.pipeline.wait_for_frames(5000)
            if frames is None:
                return None
            
            color_frame = frames.get_color_frame()
            if color_frame is None:
                return None
            
            # 转换为OpenCV格式
            data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
            image = np.reshape(data, (color_frame.get_height(), color_frame.get_width(), 3))
            image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            return image
            
        except Exception as e:
            print(f"❌ 捕获帧失败: {e}")
            return None

    def run_yolo_detection(self, image):
        """运行YOLO检测"""
        try:
            # 运行YOLO推理
            results = self.model(image, conf=CONFIDENCE_THRESHOLD, iou=IOU_THRESHOLD, verbose=False)

            if not results:
                return 0, 0.0, []

            # 获取检测结果
            detections = results[0]
            if detections.boxes is None:
                return 0, 0.0, []

            # 统计检测数量和平均置信度
            num_detections = len(detections.boxes)
            confidences = detections.boxes.conf.cpu().numpy() if num_detections > 0 else []
            avg_confidence = np.mean(confidences) if len(confidences) > 0 else 0.0

            # 获取类别信息
            classes = detections.boxes.cls.cpu().numpy() if num_detections > 0 else []
            class_names = [self.model.names[int(cls)] for cls in classes]

            return num_detections, avg_confidence, class_names

        except Exception as e:
            print(f"❌ YOLO检测失败: {e}")
            return 0, 0.0, []

    def test_parameter_combination(self, gain, gamma):
        """测试单个参数组合"""
        print(f"\n🧪 测试参数组合: 增益={gain}, 伽马={gamma}")

        # 设置相机参数
        if not self.set_camera_parameters(gain, gamma):
            return None

        # 收集测试数据
        total_detections = 0
        total_confidence = 0.0
        all_classes = []
        valid_frames = 0

        print(f"📸 开始采集 {FRAMES_PER_TEST} 帧...")

        for frame_idx in range(FRAMES_PER_TEST):
            # 捕获图像
            image = self.capture_frame()
            if image is None:
                print(f"⚠️ 第 {frame_idx + 1} 帧捕获失败")
                continue

            # YOLO检测
            num_detections, avg_confidence, class_names = self.run_yolo_detection(image)

            if num_detections > 0:
                total_detections += num_detections
                total_confidence += avg_confidence
                all_classes.extend(class_names)
                valid_frames += 1

                print(f"  帧 {frame_idx + 1}: 检测到 {num_detections} 个目标, 平均置信度: {avg_confidence:.3f}")
            else:
                print(f"  帧 {frame_idx + 1}: 未检测到目标")

            # 短暂延迟
            time.sleep(0.1)

        # 计算统计结果
        if valid_frames > 0:
            avg_detections_per_frame = total_detections / FRAMES_PER_TEST
            avg_confidence_overall = total_confidence / valid_frames
            unique_classes = list(set(all_classes))
            class_diversity = len(unique_classes)
        else:
            avg_detections_per_frame = 0
            avg_confidence_overall = 0
            unique_classes = []
            class_diversity = 0

        # 计算综合得分 (检测数量 + 置信度 + 类别多样性)
        score = (avg_detections_per_frame * 0.5 +
                avg_confidence_overall * 0.3 +
                class_diversity * 0.2)

        result = {
            'gain': gain,
            'gamma': gamma,
            'total_detections': total_detections,
            'avg_detections_per_frame': avg_detections_per_frame,
            'avg_confidence': avg_confidence_overall,
            'valid_frames': valid_frames,
            'unique_classes': unique_classes,
            'class_diversity': class_diversity,
            'score': score,
            'timestamp': datetime.now().isoformat()
        }

        print(f"📊 结果: 平均检测数={avg_detections_per_frame:.2f}, "
              f"平均置信度={avg_confidence_overall:.3f}, "
              f"类别数={class_diversity}, 综合得分={score:.3f}")

        return result

    def run_optimization(self):
        """运行参数优化"""
        print("🚀 开始YOLO参数优化测试")
        print(f"📋 测试范围: 增益 {GAIN_RANGE}, 伽马 {GAMMA_RANGE}")
        print(f"🎯 每组参数测试 {FRAMES_PER_TEST} 帧")
        print("=" * 60)

        total_combinations = len(GAIN_RANGE) * len(GAMMA_RANGE)
        current_combination = 0

        for gain in GAIN_RANGE:
            for gamma in GAMMA_RANGE:
                current_combination += 1
                print(f"\n📍 进度: {current_combination}/{total_combinations}")

                # 测试当前参数组合
                result = self.test_parameter_combination(gain, gamma)

                if result is not None:
                    self.results.append(result)

                    # 更新最佳参数
                    if result['score'] > self.best_score:
                        self.best_score = result['score']
                        self.best_params = result
                        print(f"🏆 发现更好的参数组合! 得分: {self.best_score:.3f}")

                # 短暂休息
                time.sleep(1)

        print("\n" + "=" * 60)
        print("🎉 参数优化测试完成!")
        self.print_results()
        self.save_results()

    def print_results(self):
        """打印测试结果"""
        if not self.results:
            print("❌ 没有有效的测试结果")
            return

        print(f"\n📈 测试结果汇总 (共 {len(self.results)} 组参数):")
        print("-" * 80)

        # 按得分排序
        sorted_results = sorted(self.results, key=lambda x: x['score'], reverse=True)

        print(f"{'排名':<4} {'增益':<6} {'伽马':<6} {'平均检测数':<10} {'平均置信度':<10} {'类别数':<8} {'综合得分':<10}")
        print("-" * 80)

        for i, result in enumerate(sorted_results[:10]):  # 显示前10名
            print(f"{i+1:<4} {result['gain']:<6} {result['gamma']:<6} "
                  f"{result['avg_detections_per_frame']:<10.2f} "
                  f"{result['avg_confidence']:<10.3f} "
                  f"{result['class_diversity']:<8} "
                  f"{result['score']:<10.3f}")

        if self.best_params:
            print(f"\n🏆 最佳参数组合:")
            print(f"   增益: {self.best_params['gain']}")
            print(f"   伽马: {self.best_params['gamma']}")
            print(f"   平均检测数: {self.best_params['avg_detections_per_frame']:.2f}")
            print(f"   平均置信度: {self.best_params['avg_confidence']:.3f}")
            print(f"   检测类别: {', '.join(self.best_params['unique_classes'])}")
            print(f"   综合得分: {self.best_params['score']:.3f}")

    def save_results(self):
        """保存测试结果到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"yolo_parameter_optimization_{timestamp}.json"

            data = {
                'test_config': {
                    'gain_range': GAIN_RANGE,
                    'gamma_range': GAMMA_RANGE,
                    'frames_per_test': FRAMES_PER_TEST,
                    'confidence_threshold': CONFIDENCE_THRESHOLD,
                    'iou_threshold': IOU_THRESHOLD,
                    'model_path': MODEL_PATH
                },
                'best_params': self.best_params,
                'all_results': self.results
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print(f"💾 测试结果已保存到: {filename}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            if self.pipeline:
                self.pipeline.stop()
                print("✅ Pipeline 已停止")
        except Exception as e:
            print(f"❌ 清理资源失败: {e}")

def main():
    """主函数"""
    optimizer = YOLOParameterOptimizer()

    try:
        # 初始化YOLO模型
        if not optimizer.initialize_yolo_model():
            return

        # 初始化相机
        if not optimizer.initialize_camera():
            return

        # 运行优化测试
        optimizer.run_optimization()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
    finally:
        optimizer.cleanup()
        cv2.destroyAllWindows()
        print("🔚 程序结束")

if __name__ == "__main__":
    main()
