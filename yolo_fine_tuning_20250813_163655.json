{
  "test_config": {
    "gain_range": [
      85,
      90,
      95,
      100,
      105,
      110,
      115
    ],
    "gamma_range": [
      180,
      185,
      190,
      195,
      200,
      205,
      210,
      215
    ],
    "frames_per_test": 15,
    "repeat_tests": 2,
    "confidence_threshold": 0.25,
    "iou_threshold": 0.45,
    "model_path": "train_8.pt",
    "base_result": "test3.py最佳: 增益=100, 伽马=200, 得分=4.410"
  },
  "best_params": {
    "gain": 85,
    "gamma": 180,
    "avg_score": 4.089858753085137,
    "avg_detections_per_frame": 5.1,
    "avg_confidence": 0.4661958436171214,
    "final_class_diversity": 7,
    "all_unique_classes": [
      "remote",
      "banana",
      "toothbrush",
      "jelly",
      "box",
      "hanger",
      "scissors"
    ],
    "score_stability": 0.048378445506096046,
    "detection_stability": 0.10000000000000009,
    "confidence_stability": 0.00540518164634704,
    "test_rounds": [
      {
        "round": 1,
        "total_detections": 78,
        "avg_detections_per_frame": 5.2,
        "avg_confidence": 0.46079066197077434,
        "valid_frames": 15,
        "unique_classes": [
          "remote",
          "jelly",
          "banana",
          "toothbrush",
          "scissors",
          "box",
          "hanger"
        ],
        "class_diversity": 7,
        "score": 4.1382371985912325,
        "frame_details": [
          {
            "frame_idx": 1,
            "detections": 6,
            "confidence": 