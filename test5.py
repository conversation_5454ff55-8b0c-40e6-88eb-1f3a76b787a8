#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化程序：基于test3.py和test4.py的结果进行最终优化
发现：
- test3.py最佳: 增益=100, 伽马=200, 得分=4.410
- test4.py最佳: 增益=85, 伽马=180, 得分=4.090 (类别多样性更好)
- 增益有效范围: ≤100
功能：
- 在有效参数范围内进行最终优化
- 重点测试高得分区域
- 平衡检测数量和类别多样性
"""

import cv2
import numpy as np
import time
import json
import os
from datetime import datetime
from pyorbbecsdk import Pipeline, Config, OBSensorType, OBFormat, OBPropertyID
from ultralytics import YOLO

# 基于前两次测试结果的最终优化范围
# 重点关注高得分区域：增益85-100, 伽马180-225
GAIN_RANGE = [85, 88, 92, 95, 98, 100]  # 在有效范围内密集采样
GAMMA_RANGE = [180, 185, 190, 195, 200, 205, 210, 215, 220, 225]  # 扩展伽马范围

# 固定参数
FIXED_SATURATION = 128
FIXED_CONTRAST = 64
FIXED_BRIGHTNESS = 0

# YOLO模型配置
MODEL_PATH = "train_8.pt"
CONFIDENCE_THRESHOLD = 0.25
IOU_THRESHOLD = 0.45

# 最终优化配置
FRAMES_PER_TEST = 20  # 进一步增加测试帧数
REPEAT_TESTS = 3  # 增加重复测试次数
STABILIZATION_TIME = 2

class YOLOFinalOptimizer:
    def __init__(self):
        self.model = None
        self.pipeline = None
        self.device = None
        self.config = None
        self.results = []
        self.best_params = None
        self.best_score = 0
        
    def initialize_yolo_model(self):
        """初始化YOLO模型"""
        try:
            if not os.path.exists(MODEL_PATH):
                print(f"❌ YOLO模型文件不存在: {MODEL_PATH}")
                return False
            
            self.model = YOLO(MODEL_PATH)
            print(f"✅ YOLO模型加载成功: {MODEL_PATH}")
            return True
        except Exception as e:
            print(f"❌ YOLO模型加载失败: {e}")
            return False
    
    def initialize_camera(self):
        """初始化相机"""
        try:
            self.pipeline = Pipeline()
            print("✅ Pipeline 创建成功")
            
            self.device = self.pipeline.get_device()
            if self.device is None:
                print("❌ 获取设备失败")
                return False
            
            print(f"✅ 设备信息: {self.device.get_device_info().get_name()}")
            
            # 配置颜色流
            self.config = Config()
            profile_list = self.pipeline.get_stream_profile_list(OBSensorType.COLOR_SENSOR)
            
            color_profile = None
            try:
                color_profile = profile_list.get_video_stream_profile(1920, 1080, OBFormat.RGB, 30)
                print("✅ 使用配置: 1920x1080, RGB, 30fps")
            except:
                try:
                    color_profile = profile_list.get_video_stream_profile(1280, 720, OBFormat.RGB, 30)
                    print("✅ 使用配置: 1280x720, RGB, 30fps")
                except:
                    color_profile = profile_list.get_default_video_stream_profile()
                    print("✅ 使用默认配置")
            
            if color_profile is None:
                print("❌ 无可用颜色流配置")
                return False
            
            self.config.enable_stream(color_profile)
            self.pipeline.start(self.config)
            print("✅ Pipeline 启动成功")
            
            # 关闭自动曝光
            try:
                self.device.set_bool_property(OBPropertyID.OB_PROP_COLOR_AUTO_EXPOSURE_BOOL, False)
                print("✅ 自动曝光已关闭")
            except Exception as e:
                print(f"⚠️ 关闭自动曝光失败: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 相机初始化失败: {e}")
            return False
    
    def set_camera_parameters(self, gain, gamma):
        """设置相机参数"""
        try:
            print(f"🔧 设置参数: 增益={gain}, 伽马={gamma}")
            
            # 设置增益
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_GAIN_INT, gain)
            time.sleep(0.1)
            
            # 设置伽马
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_GAMMA_INT, gamma)
            time.sleep(0.1)
            
            # 设置固定参数
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_SATURATION_INT, FIXED_SATURATION)
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_CONTRAST_INT, FIXED_CONTRAST)
            self.device.set_int_property(OBPropertyID.OB_PROP_COLOR_BRIGHTNESS_INT, FIXED_BRIGHTNESS)
            
            # 等待参数稳定
            time.sleep(STABILIZATION_TIME)
            
            # 验证参数设置
            actual_gain = self.device.get_int_property(OBPropertyID.OB_PROP_COLOR_GAIN_INT)
            actual_gamma = self.device.get_int_property(OBPropertyID.OB_PROP_COLOR_GAMMA_INT)
            
            print(f"✅ 实际参数: 增益={actual_gain}, 伽马={actual_gamma}")
            return True
            
        except Exception as e:
            print(f"❌ 参数设置失败: {e}")
            return False
    
    def capture_frame(self):
        """捕获一帧图像"""
        try:
            frames = self.pipeline.wait_for_frames(5000)
            if frames is None:
                return None
            
            color_frame = frames.get_color_frame()
            if color_frame is None:
                return None
            
            # 转换为OpenCV格式
            data = np.frombuffer(color_frame.get_data(), dtype=np.uint8)
            image = np.reshape(data, (color_frame.get_height(), color_frame.get_width(), 3))
            image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            return image
            
        except Exception as e:
            print(f"❌ 捕获帧失败: {e}")
            return None
    
    def run_yolo_detection(self, image):
        """运行YOLO检测"""
        try:
            results = self.model(image, conf=CONFIDENCE_THRESHOLD, iou=IOU_THRESHOLD, verbose=False)
            
            if not results:
                return 0, 0.0, []
            
            detections = results[0]
            if detections.boxes is None:
                return 0, 0.0, []
            
            num_detections = len(detections.boxes)
            confidences = detections.boxes.conf.cpu().numpy() if num_detections > 0 else []
            avg_confidence = np.mean(confidences) if len(confidences) > 0 else 0.0
            
            classes = detections.boxes.cls.cpu().numpy() if num_detections > 0 else []
            class_names = [self.model.names[int(cls)] for cls in classes]
            
            return num_detections, avg_confidence, class_names
            
        except Exception as e:
            print(f"❌ YOLO检测失败: {e}")
            return 0, 0.0, []

    def test_parameter_combination(self, gain, gamma):
        """测试单个参数组合，进行多次重复测试"""
        print(f"\n🧪 最终测试参数组合: 增益={gain}, 伽马={gamma}")

        # 设置相机参数
        if not self.set_camera_parameters(gain, gamma):
            return None

        # 进行多次重复测试
        all_test_results = []

        for test_round in range(REPEAT_TESTS):
            print(f"📸 第 {test_round + 1}/{REPEAT_TESTS} 轮测试，采集 {FRAMES_PER_TEST} 帧...")

            # 收集测试数据
            total_detections = 0
            total_confidence = 0.0
            all_classes = []
            valid_frames = 0
            frame_results = []

            for frame_idx in range(FRAMES_PER_TEST):
                # 捕获图像
                image = self.capture_frame()
                if image is None:
                    print(f"⚠️ 第 {frame_idx + 1} 帧捕获失败")
                    continue

                # YOLO检测
                num_detections, avg_confidence, class_names = self.run_yolo_detection(image)

                frame_result = {
                    'frame_idx': frame_idx + 1,
                    'detections': num_detections,
                    'confidence': avg_confidence,
                    'classes': class_names
                }
                frame_results.append(frame_result)

                if num_detections > 0:
                    total_detections += num_detections
                    total_confidence += avg_confidence
                    all_classes.extend(class_names)
                    valid_frames += 1

                    if frame_idx % 5 == 0 or frame_idx == FRAMES_PER_TEST - 1:  # 每5帧显示一次
                        print(f"  帧 {frame_idx + 1}: 检测到 {num_detections} 个目标, 置信度: {avg_confidence:.3f}")

                time.sleep(0.03)  # 短暂延迟

            # 计算本轮统计结果
            if valid_frames > 0:
                avg_detections_per_frame = total_detections / FRAMES_PER_TEST
                avg_confidence_overall = total_confidence / valid_frames
                unique_classes = list(set(all_classes))
                class_diversity = len(unique_classes)
            else:
                avg_detections_per_frame = 0
                avg_confidence_overall = 0
                unique_classes = []
                class_diversity = 0

            # 改进的评分公式：更重视类别多样性
            score = (avg_detections_per_frame * 0.4 +
                    avg_confidence_overall * 0.3 +
                    class_diversity * 0.3)

            test_result = {
                'round': test_round + 1,
                'total_detections': total_detections,
                'avg_detections_per_frame': avg_detections_per_frame,
                'avg_confidence': avg_confidence_overall,
                'valid_frames': valid_frames,
                'unique_classes': unique_classes,
                'class_diversity': class_diversity,
                'score': score,
                'frame_details': frame_results
            }

            all_test_results.append(test_result)

            print(f"  轮次 {test_round + 1} 结果: 检测数={avg_detections_per_frame:.2f}, "
                  f"置信度={avg_confidence_overall:.3f}, "
                  f"类别数={class_diversity}, 得分={score:.3f}")

        # 计算多轮测试的平均结果
        avg_score = np.mean([r['score'] for r in all_test_results])
        avg_detections = np.mean([r['avg_detections_per_frame'] for r in all_test_results])
        avg_confidence = np.mean([r['avg_confidence'] for r in all_test_results])

        # 合并所有轮次的类别
        all_unique_classes = set()
        for r in all_test_results:
            all_unique_classes.update(r['unique_classes'])
        final_class_diversity = len(all_unique_classes)

        # 计算稳定性指标
        score_std = np.std([r['score'] for r in all_test_results])
        detection_std = np.std([r['avg_detections_per_frame'] for r in all_test_results])
        confidence_std = np.std([r['avg_confidence'] for r in all_test_results])

        # 计算最终综合得分（考虑稳定性）
        stability_bonus = 1.0 / (1.0 + score_std)  # 稳定性奖励
        final_score = avg_score * stability_bonus

        final_result = {
            'gain': gain,
            'gamma': gamma,
            'avg_score': float(avg_score),
            'final_score': float(final_score),
            'avg_detections_per_frame': float(avg_detections),
            'avg_confidence': float(avg_confidence),
            'final_class_diversity': int(final_class_diversity),
            'all_unique_classes': list(all_unique_classes),
            'score_stability': float(score_std),
            'detection_stability': float(detection_std),
            'confidence_stability': float(confidence_std),
            'stability_bonus': float(stability_bonus),
            'test_rounds': all_test_results,
            'timestamp': datetime.now().isoformat()
        }

        print(f"📊 最终结果: 检测数={avg_detections:.2f}±{detection_std:.2f}, "
              f"置信度={avg_confidence:.3f}±{confidence_std:.3f}, "
              f"类别数={final_class_diversity}, 综合得分={final_score:.3f}")

        return final_result

    def run_final_optimization(self):
        """运行最终优化"""
        print("🎯 开始YOLO参数最终优化")
        print(f"📋 基于前期测试结果:")
        print(f"   test3.py最佳: 增益=100, 伽马=200, 得分=4.410")
        print(f"   test4.py最佳: 增益=85, 伽马=180, 得分=4.090 (类别多样性好)")
        print(f"🔍 最终优化范围: 增益 {GAIN_RANGE}, 伽马 {GAMMA_RANGE}")
        print(f"🎯 每组参数测试 {FRAMES_PER_TEST} 帧 × {REPEAT_TESTS} 轮")
        print(f"📊 改进评分公式: 检测数×0.4 + 置信度×0.3 + 类别数×0.3")
        print("=" * 80)

        total_combinations = len(GAIN_RANGE) * len(GAMMA_RANGE)
        current_combination = 0

        for gain in GAIN_RANGE:
            for gamma in GAMMA_RANGE:
                current_combination += 1
                print(f"\n📍 进度: {current_combination}/{total_combinations}")

                # 测试当前参数组合
                result = self.test_parameter_combination(gain, gamma)

                if result is not None:
                    self.results.append(result)

                    # 更新最佳参数（使用最终得分）
                    if result['final_score'] > self.best_score:
                        self.best_score = result['final_score']
                        self.best_params = result
                        print(f"🏆 发现更好的参数组合! 最终得分: {self.best_score:.3f}")

                # 短暂休息
                time.sleep(0.3)

        print("\n" + "=" * 80)
        print("🎉 最终优化测试完成!")
        self.print_results()
        self.save_results()

    def print_results(self):
        """打印测试结果"""
        if not self.results:
            print("❌ 没有有效的测试结果")
            return

        print(f"\n📈 最终优化结果汇总 (共 {len(self.results)} 组参数):")
        print("-" * 100)

        # 按最终得分排序
        sorted_results = sorted(self.results, key=lambda x: x['final_score'], reverse=True)

        print(f"{'排名':<4} {'增益':<6} {'伽马':<6} {'检测数':<10} {'置信度':<10} {'类别数':<8} {'平均得分':<10} {'最终得分':<10} {'稳定性':<8}")
        print("-" * 100)

        for i, result in enumerate(sorted_results[:15]):  # 显示前15名
            print(f"{i+1:<4} {result['gain']:<6} {result['gamma']:<6} "
                  f"{result['avg_detections_per_frame']:<10.2f} "
                  f"{result['avg_confidence']:<10.3f} "
                  f"{result['final_class_diversity']:<8} "
                  f"{result['avg_score']:<10.3f} "
                  f"{result['final_score']:<10.3f} "
                  f"{result['stability_bonus']:<8.3f}")

        if self.best_params:
            print(f"\n🏆 最终最佳参数组合:")
            print(f"   增益: {self.best_params['gain']}")
            print(f"   伽马: {self.best_params['gamma']}")
            print(f"   平均检测数: {self.best_params['avg_detections_per_frame']:.2f} ± {self.best_params['detection_stability']:.2f}")
            print(f"   平均置信度: {self.best_params['avg_confidence']:.3f} ± {self.best_params['confidence_stability']:.3f}")
            print(f"   检测类别数: {self.best_params['final_class_diversity']}")
            print(f"   检测类别: {', '.join(self.best_params['all_unique_classes'])}")
            print(f"   平均得分: {self.best_params['avg_score']:.3f} ± {self.best_params['score_stability']:.3f}")
            print(f"   稳定性奖励: {self.best_params['stability_bonus']:.3f}")
            print(f"   最终得分: {self.best_params['final_score']:.3f}")

            # 与之前结果比较
            print(f"\n📊 与之前测试结果比较:")
            print(f"   test3.py最佳: 增益=100, 伽马=200, 得分=4.410")
            print(f"   test4.py最佳: 增益=85, 伽马=180, 得分=4.090")
            print(f"   test5.py最佳: 增益={self.best_params['gain']}, 伽马={self.best_params['gamma']}, 得分={self.best_params['final_score']:.3f}")

            # 计算改进程度
            improvement_vs_test3 = ((self.best_params['final_score'] - 4.410) / 4.410) * 100
            improvement_vs_test4 = ((self.best_params['final_score'] - 4.090) / 4.090) * 100

            if improvement_vs_test3 > 0:
                print(f"   🎉 相比test3.py改进: +{improvement_vs_test3:.1f}%")
            else:
                print(f"   📊 相比test3.py变化: {improvement_vs_test3:.1f}%")

            if improvement_vs_test4 > 0:
                print(f"   🎉 相比test4.py改进: +{improvement_vs_test4:.1f}%")
            else:
                print(f"   📊 相比test4.py变化: {improvement_vs_test4:.1f}%")

    def save_results(self):
        """保存测试结果到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"yolo_final_optimization_{timestamp}.json"

            data = {
                'test_config': {
                    'gain_range': GAIN_RANGE,
                    'gamma_range': GAMMA_RANGE,
                    'frames_per_test': FRAMES_PER_TEST,
                    'repeat_tests': REPEAT_TESTS,
                    'confidence_threshold': CONFIDENCE_THRESHOLD,
                    'iou_threshold': IOU_THRESHOLD,
                    'model_path': MODEL_PATH,
                    'scoring_formula': '检测数×0.4 + 置信度×0.3 + 类别数×0.3',
                    'previous_results': {
                        'test3_best': 'gain=100, gamma=200, score=4.410',
                        'test4_best': 'gain=85, gamma=180, score=4.090'
                    }
                },
                'best_params': self.best_params,
                'all_results': self.results
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print(f"💾 最终优化结果已保存到: {filename}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            if self.pipeline:
                self.pipeline.stop()
                print("✅ Pipeline 已停止")
        except Exception as e:
            print(f"❌ 清理资源失败: {e}")

def main():
    """主函数"""
    optimizer = YOLOFinalOptimizer()

    try:
        # 初始化YOLO模型
        if not optimizer.initialize_yolo_model():
            return

        # 初始化相机
        if not optimizer.initialize_camera():
            return

        # 运行最终优化
        optimizer.run_final_optimization()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"❌ 程序运行错误: {e}")
    finally:
        optimizer.cleanup()
        cv2.destroyAllWindows()
        print("🔚 程序结束")

if __name__ == "__main__":
    main()
